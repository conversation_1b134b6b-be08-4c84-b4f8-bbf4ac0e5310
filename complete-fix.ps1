# 完整的Windows控制台乱码修复方案

Write-Host "=== Windows Console Encoding Fix ===" -ForegroundColor Cyan

# 1. 设置控制台代码页
Write-Host "1. Setting console code page to UTF-8 (65001)..." -ForegroundColor Yellow
cmd /c "chcp 65001 >nul"

# 2. 设置PowerShell编码
Write-Host "2. Setting PowerShell encoding..." -ForegroundColor Yellow
$OutputEncoding = [System.Text.Encoding]::UTF8
[Console]::InputEncoding = [System.Text.Encoding]::UTF8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

# 3. 设置环境变量
Write-Host "3. Setting environment variables..." -ForegroundColor Yellow
$env:PYTHONIOENCODING = "utf-8"
$env:LC_ALL = "en_US.UTF-8"

# 4. 显示当前设置
Write-Host "4. Current settings:" -ForegroundColor Green
Write-Host "   Console Code Page: $(cmd /c 'chcp' | Select-String '\d+' | ForEach-Object { $_.Matches[0].Value })" -ForegroundColor White
Write-Host "   PowerShell Output Encoding: $($OutputEncoding.EncodingName)" -ForegroundColor White
Write-Host "   Console Input Encoding: $([Console]::InputEncoding.EncodingName)" -ForegroundColor White
Write-Host "   Console Output Encoding: $([Console]::OutputEncoding.EncodingName)" -ForegroundColor White

# 5. 创建永久配置文件
Write-Host "5. Creating PowerShell profile for permanent fix..." -ForegroundColor Yellow
$profileContent = @"
# Auto-fix encoding on PowerShell startup
chcp 65001 | Out-Null
`$OutputEncoding = [System.Text.Encoding]::UTF8
[Console]::InputEncoding = [System.Text.Encoding]::UTF8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
"@

# 检查配置文件目录是否存在
$profileDir = Split-Path $PROFILE -Parent
if (!(Test-Path $profileDir)) {
    New-Item -ItemType Directory -Path $profileDir -Force | Out-Null
}

# 写入配置文件
$profileContent | Out-File -FilePath $PROFILE -Encoding UTF8 -Append
Write-Host "   PowerShell profile updated: $PROFILE" -ForegroundColor White

Write-Host "=== Fix completed! ===" -ForegroundColor Green
Write-Host "Note: For best results, restart your terminal or run: . `$PROFILE" -ForegroundColor Cyan
