# 修复PowerShell控制台乱码问题

# 方法1: 设置控制台代码页为UTF-8
Write-Host "Setting console code page to UTF-8..." -ForegroundColor Cyan
chcp 65001 | Out-Null

# 方法2: 设置PowerShell编码
Write-Host "Setting PowerShell encoding..." -ForegroundColor Cyan
$OutputEncoding = [System.Text.Encoding]::UTF8
[Console]::InputEncoding = [System.Text.Encoding]::UTF8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

# 方法3: 设置环境变量
$env:PYTHONIOENCODING = "utf-8"

Write-Host "Encoding fix completed!" -ForegroundColor Green
Write-Host "Current console code page: $([System.Console]::OutputEncoding.CodePage)" -ForegroundColor Yellow

# 创建一个测试文件来验证编码
@"
console.log('Hello World!');
console.log('Testing Chinese: 你好世界');
console.log('Testing special chars: éñüß');
"@ | Out-File -FilePath "test-encoding.js" -Encoding UTF8

Write-Host "Created test-encoding.js for testing" -ForegroundColor Green
